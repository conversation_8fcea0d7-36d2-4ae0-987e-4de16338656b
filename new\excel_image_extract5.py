import os
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter, column_index_from_string
from PIL import Image
import io

# === CONFIGURATION ===
input_excel = "SHALIMAR 2.xlsx"  # Use the .xlsx file
output_folder = "SHALIMAR 2 14K BOJ"
output_excel = "SHALIMAR 2 14K BOJ gram color.xlsx"
start_cell = "A4"
end_cell ="B52"

# === SETUP OUTPUT FOLDER ===
os.makedirs(output_folder, exist_ok=True)

# === LOAD THE WORKBOOK ===
wb = load_workbook(input_excel)
ws = wb.active

# === MAP IMAGES TO ANCHOR CELLS ===
image_map = {}
for image in ws._images:
    # Get cell anchor for each image
    anchor = image.anchor._from
    col_letter = get_column_letter(anchor.col + 1)
    row_number = anchor.row + 1
    cell = f"{col_letter}{row_number}"
    image_map[cell] = image

# === EXTRACT START/END COORDINATES ===
start_col = column_index_from_string(start_cell[0])
end_col = column_index_from_string(end_cell[0])
start_row = int(start_cell[1:])
end_row = int(end_cell[1:])

# === COLLECT STYLES, GRAMS, AND COLORS ===
style_gram_list = []

for col in range(start_col, end_col + 1):
    col_letter = get_column_letter(col)
    row = start_row
    while row <= end_row:
        image_cell = f"{col_letter}{row}"
        style_cell = f"{col_letter}{row + 1}"
        gram_cell = f"{col_letter}{row + 2}"

        style_no = ws[style_cell].value
        gram = ws[gram_cell].value
        print(style_no,gram)

        if image_cell in image_map and style_no:
            style_no = style_no.strip()

            # Color Logic
            if "WG" in style_no:
                color = "White"
            elif "PG" in style_no:
                color = "Pink"
            else:
                color = "Yellow"

            # Save Image
            img_bytes = image_map[image_cell]._data()
            print(type(image_map[image_cell]))
            image = Image.open(io.BytesIO(img_bytes))
            image.save(os.path.join(output_folder, f"{style_no}.png"))

            # Append collected data
            style_gram_list.append((style_no, gram, color))

        row += 3  # Move to next group

# === WRITE OUTPUT EXCEL ===
wb_out = Workbook()
ws_out = wb_out.active
ws_out.title = "Style and Gram"
ws_out.append(["Style Number", "Gram", "Color"])  # Header row

for style, gram, color in style_gram_list:
    ws_out.append([style, gram, color])

wb_out.save(output_excel)

print(f"✅ Done. Saved {len(style_gram_list)} images and created '{output_excel}'")
